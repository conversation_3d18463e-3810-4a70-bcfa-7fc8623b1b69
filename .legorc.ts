import { defineConfig } from '@shein-lego/cli'
import path from 'path'
import devServerConfig from "./dev-server-config";

const favicon = path.join(__dirname, 'favicon.ico');

export default defineConfig({
  advanced: {
    react: {
      runtime: 'automatic',
      refresh: true,
    },
    lessOptions: {
      paths: [path.resolve(__dirname, 'src/_/styles')],
      additionalData: (content, loaderContext) => {
        const { resourcePath, rootContext } = loaderContext;
        const relativePath = path.relative(rootContext, resourcePath);

        // 为每个less文件加入variate.less文件引入
        if (relativePath !== 'src/_/styles/variate.less') {
          return `@import "${rootContext + '/' + 'src/_/styles/variate.less'}"; ${content}`;
        }

        return content;
      }
    },
    postcssOptions: {
      plugins: [require('tailwindcss')],
      // 不启用，在有需要用到的时候再放开
      /*px2rem: {
        rootValue: 100,
        unitPrecision: 3,
        propList: ['*', '!border*', '!font-weight', '!flex'],
        selectorBlackList: [
          '.sm-tabs-auto',
          '.sm-dialog',
          '.sm-toast-content',
          'sm-npicker',
          'sm-toolbar',
        ],
        replace: true,
        mediaQuery: false,
        minPixelValue: 1,
        exclude: (file) => {
          /!* 当需要为更多的模块增加屏幕适配时，在这里添加正则即可 *!/
          const includeFiles = [/^.*\\src\\pages\\consult.*$/, /^.*\/src\/pages\/consult.*$/];
          return !includeFiles.some((reg) => reg.test(file));
        },
      }*/
    },
    // 启用埋点系统
    analysis: {
      appId: 'a15eca16-314c-5ca0-bade-761eaa858853',
    },
    babel: {
      presets: [
        [
          '@babel/preset-env',
          {
            // 目标浏览器配置 - 针对WebView环境
            targets: {
              // Android WebView最低版本
              android: '4.4',
              // iOS WebView最低版本
              ios: '9.0',
              // 如果需要支持更老的Chrome内核
              chrome: '49'
            },
            // 按需引入polyfill
            useBuiltIns: 'usage',
            // 指定core-js版本
            corejs: {
              version: 3,
              proposals: true
            },
            // 启用调试模式，可以看到转换了哪些语法
            debug: true,
            // 不转换模块语法（如果使用webpack等打包工具）
            modules: false,
            // 强制转换所有语法，确保兼容性
            forceAllTransforms: true,
            // 包含提案阶段的语法
            shippedProposals: true
          }
        ]
      ],
      plugins: [
        // 专门处理空值合并运算符
        '@babel/plugin-proposal-nullish-coalescing-operator',
        // 可选链操作符（如果也有兼容性问题）
        '@babel/plugin-proposal-optional-chaining',
        // 其他可能需要的插件
        '@babel/plugin-proposal-logical-assignment-operators'
      ],
      // 确保所有 node_modules 都被转换
      exclude: []
    },
    // 添加 webpack/rspack 配置来处理第三方库的现代语法
    webpack: (config: any) => {
      // 修改现有的 babel-loader 规则，确保处理所有 JavaScript 文件
      const jsRule = config.module.rules.find((rule: any) =>
        rule.test && rule.test.toString().includes('js')
      );

      if (jsRule) {
        // 移除 exclude 规则，让所有 JS 文件都被处理
        delete jsRule.exclude;
        // 或者设置一个更宽松的 exclude
        jsRule.exclude = /node_modules\/(?!(react-router|@remix-run|react-router-dom))/;
      }

      // 添加专门的规则处理第三方库
      config.module.rules.unshift({
        test: /\.js$/,
        include: /node_modules\/(react-router|@remix-run|react-router-dom)/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              [
                '@babel/preset-env',
                {
                  targets: {
                    android: '4.4',
                    ios: '9',
                    chrome: '49'
                  },
                  useBuiltIns: 'usage',
                  corejs: 3,
                  forceAllTransforms: true,
                  shippedProposals: true
                }
              ]
            ],
            plugins: [
              '@babel/plugin-proposal-nullish-coalescing-operator',
              '@babel/plugin-proposal-optional-chaining',
              '@babel/plugin-proposal-logical-assignment-operators'
            ],
            // 确保缓存失效，强制重新编译
            cacheDirectory: false
          }
        }
      });

      console.log('🔧 Webpack config updated to handle third-party libraries');
      return config;
    }
  },
  experiments: {
    legoFuture: {
      routes: {
        delay: 0,
        loading: '@/_/components/Loading',
        hoc: '@/_/components/CatchError',
      },
      // chunk加载失败时增加自动重试
      retryChunkLoad: true,
    },
    topLevelAwait: true,
    lazyCompilation: false,
  },
  publicPath: process.env.LEGO_FRONTEND_HTTP2_PUBLIC_PATH || '/',
  entry: {
    main: path.join(__dirname, 'src', 'index.tsx'),
    walletReportH5: path.join(__dirname, 'src', 'wallet-report-h5.tsx'),
    busFleet: path.join(__dirname, 'src', 'bus-fleet.tsx'),
    busDriver: path.join(__dirname, 'src', 'bus-driver.tsx'),
    employmentH5: path.join(__dirname, 'src', 'employment-h5.tsx'),
    canteenDevice: path.join(__dirname, 'src', 'canteen-device.tsx'),
  },
  htmls: [
    {
      template: path.join(__dirname, 'src', 'document.ejs'),
      title: '园区通',
      filename: "index.html",
      chunks: ['main'],
      favicon,
    },
    {
      template: path.join(__dirname, 'src', 'wallet-report-h5.ejs'),
      filename: 'walletWechatReport.html',
      title: '希音虚拟钱包简讯',
      chunks: ['walletReportH5'],
      favicon,
    },
    {
      template: path.join(__dirname, 'src', 'bus-fleet.ejs'),
      filename: 'bus-fleet.html',
      chunks: ['busFleet'],
      favicon,
    },
    {
      template: path.join(__dirname, 'src', 'bus-driver.ejs'),
      filename: 'bus-driver.html',
      chunks: ['busDriver'],
      favicon,
    },
    {
      template: path.join(__dirname, 'src', 'employment-h5.ejs'),
      filename: 'employment-h5.html',
      chunks: ['employmentH5'],
      favicon
    },
    {
      template: path.join(__dirname, 'src', 'canteen-device.ejs'),
      filename: 'canteen-device.html',
      chunks: ['canteenDevice'],
      favicon,
    }
  ],
  copy: [
    {
      from: path.resolve(__dirname, 'WW_verify_PSGFeY6KPiVNiF3j.txt')
    },
    {
      from: path.resolve(__dirname, 'MP_verify_QXA70ipsh3jidwB2.txt')
    },
  ],
  define: {
    "process.env.BUILD_TYPE": JSON.stringify(process.env.BUILD_TYPE || process.env.ENV)
  },
  devServer: devServerConfig,
})
