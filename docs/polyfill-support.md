# Canteen Device Polyfill 支持文档

## 概述

为了确保 `canteen-device` 入口在老旧的 WebView 环境中正常运行，我们添加了全面的 polyfill 支持。这包括 JavaScript 层面的 polyfill 和 HTML 模板层面的 polyfill。

## 实现方案

### 1. JavaScript 入口文件 (src/canteen-device.tsx)

在入口文件顶部添加了 `core-js/stable` 导入：

```typescript
// 导入 core-js polyfills 以支持老旧设备
import 'core-js/stable';
```

这会自动根据 Babel 配置中的 `targets` 和 `useBuiltIns: 'usage'` 设置，按需引入所需的 polyfill。

### 2. HTML 模板文件 (src/canteen-device.ejs)

在 HTML 模板的 `<head>` 部分添加了内联的 polyfill 脚本，包含以下功能：

#### 支持的 Polyfills

- **Promise**: 为不支持 Promise 的老旧环境提供基础实现
- **Object.assign**: 对象属性合并功能
- **Array.prototype.includes**: 数组包含检查
- **Array.prototype.find**: 数组查找功能
- **Array.prototype.findIndex**: 数组索引查找
- **String.prototype.includes**: 字符串包含检查
- **String.prototype.startsWith**: 字符串开头检查
- **String.prototype.endsWith**: 字符串结尾检查
- **console**: 为没有 console 对象的环境提供空实现

#### 现代语法支持检测

- **空值合并运算符 (??)**: 检测和错误处理
- **可选链操作符 (?.)**: 检测和错误处理
- **逻辑赋值运算符 (??=, ||=, &&=)**: 检测和错误处理
- **全局错误处理**: 捕获语法错误并提供诊断信息

### 3. Babel 配置 (.legorc.ts)

项目已配置了完善的 Babel 设置：

```typescript
babel: {
  presets: [
    [
      '@babel/preset-env',
      {
        // 目标浏览器配置 - 针对WebView环境
        targets: {
          android: '5.0',    // Android WebView最低版本
          ios: '10.0',       // iOS WebView最低版本
          chrome: '58'       // Chrome内核最低版本
        },
        useBuiltIns: 'usage',  // 按需引入polyfill
        corejs: {
          version: 3,
          proposals: true
        },
        debug: true,           // 启用调试模式
        modules: false
      }
    ]
  ],
  plugins: [
    '@babel/plugin-proposal-nullish-coalescing-operator',
    '@babel/plugin-proposal-optional-chaining',
    '@babel/plugin-proposal-logical-assignment-operators'
  ]
}
```

## 依赖包

项目已安装以下相关依赖：

- `@babel/polyfill`: Babel polyfill 包
- `core-js`: 现代 JavaScript polyfill 库
- `@types/core-js`: TypeScript 类型定义
- `@types/react-dom`: React DOM 类型定义

## 测试

创建了测试页面 `src/polyfill-test.html` 来验证 polyfill 功能是否正常工作。该页面包含：

- 各种 polyfill 功能的测试用例
- 可视化的测试结果展示
- 错误处理和报告

## 兼容性

通过这些 polyfill，`canteen-device` 入口现在支持：

- **Android WebView**: 5.0+
- **iOS WebView**: 10.0+
- **Chrome**: 58+
- 其他基于 Chromium 的老旧 WebView 环境

## 使用建议

1. **开发时**: 在开发过程中可以通过 Babel 的 debug 模式查看哪些 polyfill 被引入
2. **测试时**: 使用提供的测试页面验证 polyfill 功能
3. **部署时**: 确保构建过程包含了所有必要的 polyfill

## 注意事项

- HTML 模板中的 polyfill 会在页面加载时立即执行，确保后续 JavaScript 代码能正常运行
- Babel 的按需引入机制会根据代码使用情况自动添加必要的 polyfill
- 如果需要支持更老的环境，可以调整 Babel 配置中的 `targets` 设置

## 常见问题解决

### 语法错误: "Unexpected token ?"

如果遇到类似 `Uncaught SyntaxError: Unexpected token ?` 的错误，这通常是因为：

1. **空值合并运算符 (??)** 或 **可选链操作符 (?.)** 没有被正确转换
2. 第三方库中包含了现代语法，但没有被 Babel 处理
3. 特别是 `react-router` 和 `@remix-run/router` 等库使用了现代语法

**解决方案**:

1. 确保 Babel 配置包含了相关插件：
   ```javascript
   plugins: [
     '@babel/plugin-proposal-nullish-coalescing-operator',
     '@babel/plugin-proposal-optional-chaining',
     '@babel/plugin-proposal-logical-assignment-operators'
   ]
   ```

2. 在 webpack 配置中添加专门的规则处理第三方库：
   ```javascript
   webpack: (config) => {
     config.module.rules.push({
       test: /\.js$/,
       include: /node_modules\/(react-router|@remix-run|react-router-dom)/,
       use: {
         loader: 'babel-loader',
         options: {
           presets: [['@babel/preset-env', { targets: { android: '4.4', ios: '9', chrome: '49' } }]],
           plugins: [
             '@babel/plugin-proposal-nullish-coalescing-operator',
             '@babel/plugin-proposal-optional-chaining',
             '@babel/plugin-proposal-logical-assignment-operators'
           ]
         }
       }
     });
     return config;
   }
   ```

3. 如果问题持续存在，可以添加更严格的转换规则：
   ```javascript
   forceAllTransforms: true,
   shippedProposals: true
   ```

**常见问题库**:
- `@remix-run/router`: 包含空值合并运算符
- `react-router-dom`: 可能包含可选链操作符
- `react-router`: 基础路由库

### 调试现代语法支持

页面加载后，可以在控制台中运行以下命令来检查语法支持情况：

```javascript
// 检查现代语法支持
window.checkModernSyntaxSupport();

// 手动测试空值合并运算符
try {
  eval('null ?? "test"');
  console.log('空值合并运算符支持正常');
} catch (e) {
  console.error('空值合并运算符不支持:', e.message);
}
```

## 维护

当需要支持新的 JavaScript 特性或更老的环境时：

1. 更新 `.legorc.ts` 中的 `targets` 配置
2. 如有必要，在 HTML 模板中添加额外的 polyfill
3. 更新测试页面以验证新功能
4. 更新此文档
