{"name": "lpmp", "version": "1.0.0", "packageManager": "pnpm@9.2.0", "description": "物流园区管理系统", "license": "ISC", "scripts": {"init-deps": "pnpm install --frozen-lockfile", "start": "npm run dev", "dev": "ENV=dev lego dev", "test": "ENV=test lego dev", "test:debug": "DEBUG=1 ENV=test lego dev", "test:analyzer": "ANALYZER=1 ENV=test lego dev", "sit": "ENV=sit lego dev", "build": "ENV=prod lego build", "lint": "eslint src/**/*.{ts,tsx} --fix", "prettier": "prettier --write .", "prepare": "husky install"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-async-to-generator": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.27.2", "@babel/plugin-transform-runtime": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "@shein-bbl/eslint-plugin-bbl": "1.3.3-beta.1", "@shein-lego/apis": "^0.25.2", "@shein-lego/cli": "^1.9.0", "@shein-lego/plugin-externals-map": "^0.25.2", "@shein-lego/styled": "^0.9.1-alpha.2", "@types/core-js": "^2.5.8", "@types/react-dom": "^17.0.26", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "@wms/husky-rules": "0.3.3", "@wms/shineout-mobile-ts": "^4.0.10-rc.2", "caniuse-lite": "^1.0.30001627", "core-js": "^3.42.0", "eslint-plugin-simple-import-sort": "^7.0.0", "eslint-plugin-unused-imports": "^3.0.0", "html-webpack-plugin": "^5.5.1", "husky": "^7.0.0", "lint-staged": "^12.4.0", "mrm": "^4.1.13", "postcss-less": "^6.0.0", "postcss-pxtorem": "^6.0.0", "stylelint": "^14.7.1", "stylelint-config-prettier": "^9.0.3", "stylelint-order": "^5.0.0", "typescript": "^4.5.5", "typescript-plugin-css-modules": "^3.4.0", "vconsole": "^3.15.1"}, "dependencies": {"@shein-bbl/core": "1.3.3-beta.1", "@shein-bbl/react": "1.3.3-beta.1", "@shein-components/Icon": "^3.1.6", "@shein-components/qr-code": "^1.1.1", "@shein-components/sso-sdk": "^1.0.1", "@shein-components/WatermarkSdk": "0.0.11", "@shein-components/XssText": "^0.0.4", "@shein-lego/ak": "^0.1.0", "@shein-lego/ak.macro": "^0.1.0", "@shein-lego/use": "^0.0.1-alpha.4", "@shein/apm-helper": "0.0.5", "@types/js-cookie": "^3.0.6", "@types/numeral": "^2.0.5", "@types/react": "^17.0.45", "ahooks": "^3.7.8", "axios": "^0.27.2", "classnames": "^2.5.1", "html-webpack-plugin": "^5.5.1", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "npm": "^8.10.0", "numeral": "^2.0.6", "p-min-delay": "^4.0.2", "qrcode.react": "^3.1.0", "query-string": "^7.1.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-hook-form": "^7.48.2", "react-router-dom": "^6.4.3", "react-textarea-autosize": "^8.4.1", "react-transition-group": "^4.4.5", "redux": "^4.1.2", "redux-devtools-extension": "^2.13.9", "sheinq": "^2.0.23", "shineout-mobile": "^4.0.10-rc.12", "store2": "^2.14.2", "tailwindcss": "^3.4.1", "unstated-next": "^1.1.0", "uuid": "^9.0.1", "vehicle-plate-keyboard": "^0.8.4"}}