<!doctype html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="format-detection" content="email=no" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />
    <style>
        #startPageWrap {
            position: fixed;
            top: 0;
            left: 0;
            z-index: -1;
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100vh;
            justify-content: center;
            align-items: center;
        }

        /* 加载层样式 */
        .startPageLoading {
            width: 48px;
            height: 48px;
            border: 5px solid #0059ce;
            border-bottom-color: transparent;
            border-radius: 50%;
            display: inline-block;
            box-sizing: border-box;
            animation: rotation 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes rotation {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <div id="startPageWrap">
            <div class="startPageLoading"></div>
            <p style="margin-top: 20px; font-size: 14px; color: #b9b9b9">
                正在打开系统,请稍等...12345
            </p>
        </div>
    </div>
</body>
</html>