<!doctype html>
<html>

<head>
    <meta charset="utf-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="format-detection" content="telephone=no" />
    <meta name="format-detection" content="email=no" />
    <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no" />

    <!-- Polyfills for older browsers and WebView environments -->
    <script>
        // 基础 polyfills 用于老旧 WebView 环境
        (function() {
            // 空值合并运算符 (??) polyfill
            if (typeof window !== 'undefined' && !window.hasOwnProperty('__nullishCoalescingSupported')) {
                // 检测是否支持空值合并运算符
                try {
                    eval('null ?? "test"');
                    window.__nullishCoalescingSupported = true;
                } catch (e) {
                    // 不支持，需要通过 Babel 转换处理
                    window.__nullishCoalescingSupported = false;
                }
            }

            // 可选链操作符 (?.) polyfill 检测
            if (typeof window !== 'undefined' && !window.hasOwnProperty('__optionalChainingSupported')) {
                try {
                    eval('({})?.test');
                    window.__optionalChainingSupported = true;
                } catch (e) {
                    window.__optionalChainingSupported = false;
                }
            }

            // Promise polyfill for very old environments
            if (typeof Promise === 'undefined') {
                window.Promise = function(executor) {
                    var self = this;
                    self.state = 'pending';
                    self.value = undefined;
                    self.handlers = [];

                    function resolve(result) {
                        if (self.state === 'pending') {
                            self.state = 'fulfilled';
                            self.value = result;
                            self.handlers.forEach(handle);
                            self.handlers = null;
                        }
                    }

                    function reject(error) {
                        if (self.state === 'pending') {
                            self.state = 'rejected';
                            self.value = error;
                            self.handlers.forEach(handle);
                            self.handlers = null;
                        }
                    }

                    function handle(handler) {
                        if (self.state === 'pending') {
                            self.handlers.push(handler);
                        } else {
                            if (self.state === 'fulfilled' && typeof handler.onFulfilled === 'function') {
                                handler.onFulfilled(self.value);
                            }
                            if (self.state === 'rejected' && typeof handler.onRejected === 'function') {
                                handler.onRejected(self.value);
                            }
                        }
                    }

                    this.then = function(onFulfilled, onRejected) {
                        return new Promise(function(resolve, reject) {
                            handle({
                                onFulfilled: function(result) {
                                    try {
                                        resolve(onFulfilled ? onFulfilled(result) : result);
                                    } catch (ex) {
                                        reject(ex);
                                    }
                                },
                                onRejected: function(error) {
                                    try {
                                        resolve(onRejected ? onRejected(error) : error);
                                    } catch (ex) {
                                        reject(ex);
                                    }
                                }
                            });
                        });
                    };

                    executor(resolve, reject);
                };
            }

            // Object.assign polyfill
            if (typeof Object.assign !== 'function') {
                Object.assign = function(target) {
                    if (target == null) {
                        throw new TypeError('Cannot convert undefined or null to object');
                    }
                    var to = Object(target);
                    for (var index = 1; index < arguments.length; index++) {
                        var nextSource = arguments[index];
                        if (nextSource != null) {
                            for (var nextKey in nextSource) {
                                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                                    to[nextKey] = nextSource[nextKey];
                                }
                            }
                        }
                    }
                    return to;
                };
            }

            // Array.prototype.includes polyfill
            if (!Array.prototype.includes) {
                Array.prototype.includes = function(searchElement, fromIndex) {
                    if (this == null) {
                        throw new TypeError('"this" is null or not defined');
                    }
                    var o = Object(this);
                    var len = parseInt(o.length) || 0;
                    if (len === 0) {
                        return false;
                    }
                    var n = parseInt(fromIndex) || 0;
                    var k;
                    if (n >= 0) {
                        k = n;
                    } else {
                        k = len + n;
                        if (k < 0) {
                            k = 0;
                        }
                    }
                    function sameValueZero(x, y) {
                        return x === y || (typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y));
                    }
                    for (; k < len; k++) {
                        if (sameValueZero(o[k], searchElement)) {
                            return true;
                        }
                    }
                    return false;
                };
            }

            // String.prototype.includes polyfill
            if (!String.prototype.includes) {
                String.prototype.includes = function(search, start) {
                    if (typeof start !== 'number') {
                        start = 0;
                    }
                    if (start + search.length > this.length) {
                        return false;
                    } else {
                        return this.indexOf(search, start) !== -1;
                    }
                };
            }

            // String.prototype.startsWith polyfill
            if (!String.prototype.startsWith) {
                String.prototype.startsWith = function(searchString, position) {
                    position = position || 0;
                    return this.substr(position, searchString.length) === searchString;
                };
            }

            // String.prototype.endsWith polyfill
            if (!String.prototype.endsWith) {
                String.prototype.endsWith = function(searchString, length) {
                    if (length === undefined || length > this.length) {
                        length = this.length;
                    }
                    return this.substring(length - searchString.length, length) === searchString;
                };
            }

            // Array.prototype.find polyfill
            if (!Array.prototype.find) {
                Array.prototype.find = function(predicate) {
                    if (this == null) {
                        throw new TypeError('"this" is null or not defined');
                    }
                    var o = Object(this);
                    var len = parseInt(o.length) || 0;
                    if (typeof predicate !== 'function') {
                        throw new TypeError('predicate must be a function');
                    }
                    var thisArg = arguments[1];
                    var k = 0;
                    while (k < len) {
                        var kValue = o[k];
                        if (predicate.call(thisArg, kValue, k, o)) {
                            return kValue;
                        }
                        k++;
                    }
                    return undefined;
                };
            }

            // Array.prototype.findIndex polyfill
            if (!Array.prototype.findIndex) {
                Array.prototype.findIndex = function(predicate) {
                    if (this == null) {
                        throw new TypeError('"this" is null or not defined');
                    }
                    var o = Object(this);
                    var len = parseInt(o.length) || 0;
                    if (typeof predicate !== 'function') {
                        throw new TypeError('predicate must be a function');
                    }
                    var thisArg = arguments[1];
                    var k = 0;
                    while (k < len) {
                        var kValue = o[k];
                        if (predicate.call(thisArg, kValue, k, o)) {
                            return k;
                        }
                        k++;
                    }
                    return -1;
                };
            }

            // console polyfill for environments without console
            if (typeof console === 'undefined') {
                window.console = {
                    log: function() {},
                    error: function() {},
                    warn: function() {},
                    info: function() {},
                    debug: function() {}
                };
            }

            // 添加一个全局的错误处理器来捕获语法错误
            window.addEventListener('error', function(event) {
                if (event.error && event.error.message) {
                    var message = event.error.message;
                    if (message.indexOf('Unexpected token') !== -1 &&
                        (message.indexOf('?') !== -1 || message.indexOf('?.') !== -1 || message.indexOf('??') !== -1)) {
                        console.warn('检测到现代 JavaScript 语法错误，可能需要更新 Babel 配置或浏览器版本');
                        console.warn('错误详情:', message);
                        console.warn('文件:', event.filename, '行号:', event.lineno);

                        // 尝试重新加载页面（可选）
                        // window.location.reload();
                    }
                }
            });

            // 添加一个检测函数来验证现代语法支持
            window.checkModernSyntaxSupport = function() {
                var support = {
                    nullishCoalescing: false,
                    optionalChaining: false,
                    logicalAssignment: false
                };

                try {
                    // 检测空值合并运算符
                    eval('null ?? "test"');
                    support.nullishCoalescing = true;
                } catch (e) {
                    console.warn('空值合并运算符 (??) 不被支持');
                }

                try {
                    // 检测可选链操作符
                    eval('({})?.test');
                    support.optionalChaining = true;
                } catch (e) {
                    console.warn('可选链操作符 (?.) 不被支持');
                }

                try {
                    // 检测逻辑赋值运算符
                    eval('var x = null; x ??= "test"');
                    support.logicalAssignment = true;
                } catch (e) {
                    console.warn('逻辑赋值运算符 (??=) 不被支持');
                }

                return support;
            };

            // 立即检测支持情况
            if (typeof window !== 'undefined') {
                setTimeout(function() {
                    var support = window.checkModernSyntaxSupport();
                    console.log('现代 JavaScript 语法支持情况:', support);
                }, 100);
            }
        })();
    </script>
    <style>
        #startPageWrap {
            position: fixed;
            top: 0;
            left: 0;
            z-index: -1;
            display: flex;
            flex-direction: column;
            width: 100%;
            height: 100vh;
            justify-content: center;
            align-items: center;
        }

        /* 加载层样式 */
        .startPageLoading {
            width: 48px;
            height: 48px;
            border: 5px solid #0059ce;
            border-bottom-color: transparent;
            border-radius: 50%;
            display: inline-block;
            box-sizing: border-box;
            animation: rotation 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes rotation {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body>
    <div id="app">
        <div id="startPageWrap">
            <div class="startPageLoading"></div>
            <p style="margin-top: 20px; font-size: 14px; color: #b9b9b9">
                正在打开系统,请稍等...12345
            </p>
        </div>
    </div>
</body>
</html>