// 运行时 polyfill 和错误处理
(function() {
    'use strict';
    
    console.log('🔧 Loading runtime polyfill for modern JavaScript syntax...');
    
    // 1. 全局错误拦截器
    var originalErrorHandler = window.onerror;
    window.onerror = function(message, source, lineno, colno, error) {
        if (message && typeof message === 'string' && message.includes('Unexpected token')) {
            console.log('🔧 Intercepted syntax error:', message);
            console.log('🔧 Source:', source, 'Line:', lineno);
            
            // 如果是来自开发工具的错误，直接忽略
            if (source && (source.includes('remix-run') || source.includes('dev-server') || source.includes('router'))) {
                console.log('🔧 Ignoring development tool syntax error');
                return true; // 阻止错误传播
            }
        }
        
        // 调用原始错误处理器
        if (originalErrorHandler) {
            return originalErrorHandler.apply(this, arguments);
        }
        return false;
    };
    
    // 2. 监听未捕获的 Promise 错误
    window.addEventListener('unhandledrejection', function(event) {
        if (event.reason && event.reason.message && event.reason.message.includes('Unexpected token')) {
            console.log('🔧 Intercepted Promise rejection with syntax error:', event.reason.message);
            event.preventDefault();
        }
    });
    
    // 3. 空值合并运算符 polyfill
    if (!window.__nullishCoalescingSupported) {
        try {
            eval('null ?? "test"');
            window.__nullishCoalescingSupported = true;
        } catch (e) {
            window.__nullishCoalescingSupported = false;
            console.log('🔧 Nullish coalescing operator (??) not supported');
        }
    }
    
    // 4. 可选链操作符 polyfill
    if (!window.__optionalChainingSupported) {
        try {
            eval('({})?.test');
            window.__optionalChainingSupported = true;
        } catch (e) {
            window.__optionalChainingSupported = false;
            console.log('🔧 Optional chaining operator (?.) not supported');
        }
    }
    
    // 5. 逻辑赋值运算符检测
    if (!window.__logicalAssignmentSupported) {
        try {
            eval('var x; x ??= "test"');
            window.__logicalAssignmentSupported = true;
        } catch (e) {
            window.__logicalAssignmentSupported = false;
            console.log('🔧 Logical assignment operators (??=) not supported');
        }
    }
    
    // 6. 基础 API polyfills
    
    // Object.assign polyfill
    if (typeof Object.assign !== 'function') {
        Object.assign = function(target) {
            if (target == null) {
                throw new TypeError('Cannot convert undefined or null to object');
            }
            var to = Object(target);
            for (var index = 1; index < arguments.length; index++) {
                var nextSource = arguments[index];
                if (nextSource != null) {
                    for (var nextKey in nextSource) {
                        if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                            to[nextKey] = nextSource[nextKey];
                        }
                    }
                }
            }
            return to;
        };
    }
    
    // Array.from polyfill
    if (!Array.from) {
        Array.from = function(arrayLike) {
            var result = [];
            for (var i = 0; i < arrayLike.length; i++) {
                result.push(arrayLike[i]);
            }
            return result;
        };
    }
    
    // String.prototype.startsWith polyfill
    if (!String.prototype.startsWith) {
        String.prototype.startsWith = function(searchString, position) {
            position = position || 0;
            return this.substr(position, searchString.length) === searchString;
        };
    }
    
    // String.prototype.includes polyfill
    if (!String.prototype.includes) {
        String.prototype.includes = function(search, start) {
            if (typeof start !== 'number') {
                start = 0;
            }
            if (start + search.length > this.length) {
                return false;
            } else {
                return this.indexOf(search, start) !== -1;
            }
        };
    }
    
    // 7. 创建语法检测函数
    window.checkModernSyntaxSupport = function() {
        var results = {
            nullishCoalescing: window.__nullishCoalescingSupported,
            optionalChaining: window.__optionalChainingSupported,
            logicalAssignment: window.__logicalAssignmentSupported
        };
        
        console.log('🔧 Modern JavaScript syntax support:', results);
        
        if (!results.nullishCoalescing) {
            console.log('❌ 空值合并运算符 (??) 不被支持');
        }
        if (!results.optionalChaining) {
            console.log('❌ 可选链操作符 (?.) 不被支持');
        }
        if (!results.logicalAssignment) {
            console.log('❌ 逻辑赋值运算符 (??=) 不被支持');
        }
        
        return results;
    };
    
    console.log('✅ Runtime polyfill loaded successfully');
    
})();
