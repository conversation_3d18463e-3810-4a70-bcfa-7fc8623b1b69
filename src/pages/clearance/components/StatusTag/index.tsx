import React from 'react';
import classNames from 'classnames';
import { applyStatusMap, EApplyStatus } from '../../add/interfaces';
import styles from './index.less';

interface IMainProps {
  status: EApplyStatus;
}

const statusClassMap = {
  [EApplyStatus.WAIT_SUBMIT]: styles.waitSubmit,
  [EApplyStatus.APPROVAL]: styles.approval,
  [EApplyStatus.REJECT]: styles.reject,
  [EApplyStatus.FINISHED]: styles.finished,
  [EApplyStatus.WITHDRAW]: styles.withdraw,
  [EApplyStatus.CANCEL]: styles.cancel,
};

/**
 * @description Main
 * @param {unknown} props desc
 * @returns {unknown} desc
 */
const Main: React.FC<IMainProps> = (props) => {
  const { status } = props;
  return (
    <div className={classNames(styles.tag, statusClassMap[status])}>{applyStatusMap[status]}</div>
  );
};

export default Main;
