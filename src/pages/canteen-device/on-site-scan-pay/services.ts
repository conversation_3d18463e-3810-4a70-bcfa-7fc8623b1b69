import { post } from '../_/utils/fetch';
import cancelOrderMock from './mock/cancelOrder.json';
// 导入 mock 数据
import scanPayMock from './mock/scanPay.json';

export interface IScanPayParams {
  /** 消费二维码 */
  qrcode: string;
}

export interface IScanPayResponse {
  /** 订单id */
  id?: number;
  /** 订单号 */
  orderNo?: string;
  /** 订单总金额 */
  totalAmount?: number;
  /** 钱包支付金额 */
  walletPayAmount?: number;
  /** 差额 */
  margin?: number;
  /** 微信支付单号 */
  weChatPayOrderNo?: string;
  /** 钱包支付单号 */
  walletOderNo?: string;
  /** 微信支付链接 */
  weChatPayUrl?: string;
}

/**
 * 食堂设备端扫码订单支付
 * https://soapi.sheincorp.cn/application/3694/routes/245373/doc
 * @param {IScanPayParams} params
 * @returns {Promise<IScanPayResponse>} 返回值
 */
export const scanPayAPI = (params: IScanPayParams) => {
  // 开发环境使用 mock 数据
  if (process.env.NODE_ENV === 'development') {
    return Promise.resolve(scanPayMock.data);
  }
  return post<IScanPayResponse>('/canteen/device/order/scanPay', params);
};

export interface ICancelOrderParams {
  /** 食堂订单号 */
  orderNo: string;
}

/**
 * 食堂设备端订单取消
 * https://soapi.sheincorp.cn/application/3694/routes/245374/doc
 * @param {ICancelOrderParams} params
 * @returns {Promise<boolean>} 返回值
 */
export const cancelOrderAPI = (params: ICancelOrderParams) => {
  // 开发环境使用 mock 数据
  if (process.env.NODE_ENV === 'development') {
    return Promise.resolve(cancelOrderMock.data);
  }
  return post<boolean>('/canteen/device/order/cancel', params);
};
