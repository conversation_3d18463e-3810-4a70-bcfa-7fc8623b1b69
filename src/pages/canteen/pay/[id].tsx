import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Page, PageLoading } from '@/_/components';
import Icon from '@/_/components/Icon';
import { t } from '@shein-bbl/react';
import { useMount, useRequest } from 'ahooks';
import { Toast } from 'shineout-mobile';
import { getConsumerBalanceAPI, getOrderDetail } from '../_/api';
import OrderCard from '../_/components/order-card';
import { ECanteenPayChannelType } from '../_/interfaces';
import { EOrderStatus } from '../_/interfaces/order';
import PayBar from './components/pay-bar';
import PayRadio from './components/pay-radio';
import PaySuccess from './components/pay-success';
import styles from './index.less';

const PayPage = () => {
  const navigate = useNavigate();
  const { id: orderId } = useParams();

  const [walletBalance, setWalletBalance] = useState(0);
  const [payType, setPayType] = useState<ECanteenPayChannelType>();

  const {
    data: orderInfo,
    loading: orderLoading,
    refresh: refreshOrder,
  } = useRequest(getOrderDetail, {
    manual: !orderId,
    defaultParams: [{ orderId }],
  });

  const { loading: consumerBalanceLoading } = useRequest(getConsumerBalanceAPI, {
    manual: !orderId,
    onSuccess: (res) => {
      setWalletBalance(res?.total || 0);
      setPayType(
        res?.total > 0 ? ECanteenPayChannelType.VIRTUAL_WALLET : ECanteenPayChannelType.WECHAT,
      );
    },
  });

  useMount(() => {
    if (!orderId) {
      Toast.fail(t('访问参数错误'));
    }
  });

  if (orderInfo?.orderStatus === EOrderStatus.RESERVED) {
    return (
      <PaySuccess
        onBackHome={() =>
          navigate(`/canteen/home${orderInfo?.date ? `?date=${orderInfo?.date}` : ''}`, {
            replace: true,
          })
        }
      />
    );
  }

  if (!orderInfo || orderLoading || consumerBalanceLoading) {
    return <PageLoading title={t('确认支付')} />;
  }

  const hasWalletBalance = walletBalance > 0;
  const { orderNo, mealTimePeriodTypeName, date, canteenName, stallName, orderDetail } = orderInfo;

  return (
    <Page
      title={t('确认支付')}
      footerClassName="pt-0 pl-0 pr-0"
      footer={
        <PayBar
          payChannelType={payType}
          orderNo={orderNo}
          dishes={orderDetail || []}
          onRefreshOrder={refreshOrder}
        />
      }
    >
      <OrderCard
        mealTimePeriodTypeName={mealTimePeriodTypeName}
        date={date}
        canteenName={canteenName}
        stallName={stallName}
        dishes={orderDetail || []}
      />
      {/* 支付方式 */}
      <div className={styles.pay}>
        <div className={styles.payTitle}>{t('支付方式')}</div>

        <PayRadio
          checked={payType === ECanteenPayChannelType.VIRTUAL_WALLET}
          disabled={!hasWalletBalance}
          onChange={() => setPayType(ECanteenPayChannelType.VIRTUAL_WALLET)}
        >
          <div>
            <Icon name="pc-charge-shineout-fill" className={styles.payWalletIcon} fontSize={16} />
            <span>{t('钱包余额')}</span>
            {hasWalletBalance ? (
              <span className={styles.payBalance}>
                ¥{walletBalance}
                {t('可用')}
              </span>
            ) : (
              <span className={styles.payBalanceZero}>{t('无可用余额')}</span>
            )}
          </div>
        </PayRadio>
        <PayRadio
          checked={payType === ECanteenPayChannelType.WECHAT}
          onChange={() => setPayType(ECanteenPayChannelType.WECHAT)}
        >
          <div>
            <Icon name="m-wechat-shineout-fill" className={styles.payWechatIcon} fontSize={16} />
            <span>{t('微信支付')}</span>
          </div>
        </PayRadio>
      </div>
    </Page>
  );
};

export default PayPage;
