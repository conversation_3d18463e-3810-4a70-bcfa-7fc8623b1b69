import { useNavigate } from 'react-router-dom';
import { cancelCanteenOrder } from '@/pages/canteen/_/api';
import { ICanteenPayResponse } from '@/pages/canteen/_/interfaces';
import { t } from '@shein-bbl/react';
import { useRequest } from 'ahooks';
import { But<PERSON>, Drawer } from 'shineout-mobile';
import styles from './index.less';

interface RemainingPayProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
  info: ICanteenPayResponse;
  loading: boolean;
}

/**
 * @description 微信支付差额
 * @param {RemainingPayProps} props 参数
 * @returns {React.ReactElement} RemainingPay
 */
const RemainingPay = ({
  visible,
  onClose,
  onConfirm,
  info,
  loading,
}: RemainingPayProps): React.ReactElement => {
  const { runAsync: cancelOrder, loading: cancelOrderLoading } = useRequest(cancelCanteenOrder, {
    manual: true,
  });
  const navigate = useNavigate();
  /**
   * @description 取消
   * @returns {void}
   */
  const handleCancel = (): void => {
    cancelOrder(info.orderNo).then(() => {
      onClose();
      navigate('/canteen/dish', { replace: true });
    });
  };
  return (
    <Drawer round height="auto" visible={visible} onClose={onClose}>
      <div className={styles.remainingPayBox}>
        <div className={styles.remainingPayTitle}>
          {t('钱包可用余额不足，是否使用微信支付差额')}
          <span className={styles.remainingPayAmount}>¥{info.margin}</span>
        </div>
        <div className={styles.remainingPayTip}>
          {t('若取消支付，钱包余额将在 {} 分钟 内退回', 1)}
        </div>
        <div className={styles.remainingPayList}>
          <div className={styles.remainingPayItem}>
            <span>{t('合计需支付')}</span>
            <span className={styles.remainingPayValue}>¥{info.totalAmount}</span>
          </div>
          <div className={styles.remainingPayItem}>
            <span>{t('钱包余额')}</span>
            <span className={styles.remainingPayValue}>¥{info.walletPayAmount}</span>
          </div>
          <div className={styles.remainingPayItem}>
            <span>{t('微信支付差额')}</span>
            <span className={styles.remainingPayDiff}>¥{info.margin}</span>
          </div>
        </div>
        <Button
          className={styles.remainingPayBtn}
          type="primary"
          loading={loading || cancelOrderLoading}
          onClick={onConfirm}
        >
          {t('使用微信支付差额')}
        </Button>
        <Button
          className={styles.remainingPayCancel}
          onClick={handleCancel}
          loading={cancelOrderLoading}
        >
          {t('取消')}
        </Button>
      </div>
    </Drawer>
  );
};

export default RemainingPay;
