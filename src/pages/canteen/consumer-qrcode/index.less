/* stylelint-disable max-nesting-depth */
.container {
  position: relative;
  display: flex;
  height: 100vh;
  padding: 12px;
  flex-direction: column;

  .code {
    display: flex;
    height: 320px;
    background-color: white;
    border-radius: 4px;
    align-items: center;
    flex-direction: column;

    .qrCode {
      margin: 48px 0 0;
    }

    .subDes {
      display: flex;
      flex-direction: row;
      width: 280px;
      align-items: center;
      justify-content: space-between;
      text-align: center;

      p {
        padding: 14px;
        font-size: 12px;
        color: #666c7c;
        flex-grow: 1;
      }
    }
  }

  .walletInfo {
    overflow-y: auto;
    background-color: #f7f8fa;
    border-radius: 4px;
    flex-grow: 1;

    .walletCard {
      display: flex;
      padding: 12px;
      border-radius: 4px;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;

      .title {
        display: flex;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        align-items: center;
      }

      .balanceRow {
        display: flex;
      }

      .balanceAmount {
        margin-right: 6px;
        font-size: 18px;
        font-weight: 500;
        color: #141737;
        flex-grow: 1;
      }

      .detailButton {
        font-size: 20px;
        color: #666c7c;
      }
    }
  }
}

.balanceDetailDialog {
  padding: 10px 0;

  .totalBalance {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
    margin-bottom: 10px;

    .label {
      font-weight: 500;
      color: #666c7c;
    }

    .value {
      font-weight: 500;
      color: #141737;
    }
  }

  .detailItem {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;

    .accountName {
      color: #666c7c;
    }

    .accountBalance {
      color: #141737;
    }
  }

  .dialogButton {
    width: 100%;
    margin-top: 20px;
  }
}

.customModalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000; //确保在最上层
}

.customModal {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 400px; // 可以设置一个最大宽度
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.customModalTitle {
  margin-bottom: 15px;
  word-break: break-all; // 防止标题过长溢出
  color: #141737;
  text-align: center;
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: 26px;
}

.customModalContent {
  margin-bottom: 20px;

  .paymentDetails {
    .paymentRow {
      display: flex;
      justify-content: space-between;
      padding: 8px 0;
      color: #999DA8;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;

      span:last-child {
        color: #141737;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }

    .paymentRowHighlightRed {
      // 与图片一致的红色高亮
      span {
        color: #f56c0a; // 或者您图片中的红色
        font-weight: bold;
      }

      span:last-child {
        color: #f56c0a;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }
  }
}

.customModalFooter {
  display: flex;
  justify-content: space-between;
  flex-direction: column;


  .customModalButton {
    flex: 1;
    padding: 16px;
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }

  .customModalButtonPrimary {
    color: white;
    margin-bottom: 12px;
  }
}