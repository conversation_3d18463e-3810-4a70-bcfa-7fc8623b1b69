import { useCallback, useEffect, useRef, useState } from 'react';
import { usePageTitle } from '@/_/hooks';
import Icon from '@shein-components/Icon';
import QRCode from '@shein-components/qr-code';
import { t } from '@shein-bbl/react';
import { usePersistFn } from '@shein-lego/use';
import { useBoolean, useMount, useUnmount } from 'ahooks';
import { Button, Dialog, Toast } from 'shineout-mobile';
import styles from './index.less';
import { IConsumerBalanceInfo, IConsumerResultResponse } from './interfaces';
import { getConsumerBalanceAPI, getConsumerQrcodeAPI, getConsumerResultAPI } from './serivces';

// 刷新二维码的间隔时间（毫秒）
const INTERVAL_TIME = 3 * 60 * 1000; // 3分钟

// 轮询获取支付结果的间隔时间（毫秒）
const POLLING_INTERVAL_TIME = 5 * 1000; // 5秒

const ConsumerQrcodePage = () => {
  usePageTitle(t('消费码'));
  // 控制二维码加载状态
  const [loading, setLoading] = useState(false);
  // 当前消费码的值
  const [codeValue, setCodeValue] = useState('');
  // 钱包余额信息
  const [balanceInfo, setBalanceInfo] = useState<IConsumerBalanceInfo>({});
  const intervalRef = useRef<NodeJS.Timeout>();
  const pollingIntervalRef = useRef<NodeJS.Timeout>();
  // 控制微信支付弹窗显示/隐藏
  const [showWechatPayModal, { setTrue: openWechatPayModal, setFalse: closeWechatPayModal }] =
    useBoolean(false);
  // 当前支付信息（用于微信支付弹窗）
  const [paymentInfo, setPaymentInfo] = useState<IConsumerResultResponse | null>(null);

  const startIntervalGetCode = usePersistFn(() => {
    intervalRef.current = setInterval(() => {
      handleGetConsumerQrcode();
    }, INTERVAL_TIME);
  });

  /**
   * @description 获取消费码
   */
  const handleGetConsumerQrcode = () => {
    setLoading(true);
    getConsumerQrcodeAPI({
      refresh: true,
    })
      .then((res) => {
        setCodeValue(res);
      })
      .catch(() => {
        Toast.fail(t('获取消费码失败'));
      })
      .finally(() => {
        setLoading(false);
      });
  };

  /**
   * @description 获取钱包余额信息
   */
  const handleGetBalanceInfo = useCallback(() => {
    getConsumerBalanceAPI()
      .then((res) => {
        setBalanceInfo(res);
      })
      .catch(() => {
        Toast.fail(t('获取余额信息失败'));
      });
  }, []);

  /**
   * @description 处理支付结果
   */
  const handlePaymentResult = usePersistFn((result: IConsumerResultResponse) => {
    switch (result.paySuccess) {
      case 0:
        // 继续轮询
        break;
      case 1:
        Toast.success(t('支付成功'));
        // 3秒后继续轮询，这里不需要清除再设置，轮询会自动进行下一次
        break;
      case 2:
        setPaymentInfo(result);
        openWechatPayModal();
        // 暂停轮询，直到用户操作完成
        if (pollingIntervalRef.current) {
          clearInterval(pollingIntervalRef.current);
        }
        break;
      default:
        break;
    }
  });

  /**
   * @description 轮询获取支付结果
   */
  const pollGetConsumerResult = usePersistFn(() => {
    getConsumerResultAPI()
      .then((res) => {
        if (res) {
          handlePaymentResult(res);
        }
      })
      .catch(() => {
        // 接口失败也继续轮询，或者根据产品策略决定是否提示
      });
  });

  /**
   * @description 启动支付结果轮询
   */
  const startPolling = usePersistFn(() => {
    // 先执行一次，然后开启定时器
    pollGetConsumerResult();
    pollingIntervalRef.current = setInterval(pollGetConsumerResult, POLLING_INTERVAL_TIME);
  });

  /**
   * @description 停止支付结果轮询
   */
  const stopPolling = usePersistFn(() => {
    if (pollingIntervalRef.current) {
      clearInterval(pollingIntervalRef.current);
    }
  });

  useMount(() => {
    handleGetBalanceInfo();
    handleGetConsumerQrcode();
    startIntervalGetCode();
    startPolling();
  });

  useUnmount(() => {
    clearInterval(intervalRef.current);
    stopPolling();
  });

  /**
   * @description 刷新二维码
   */
  const handleRefreshCode = () => {
    clearInterval(intervalRef.current);
    handleGetConsumerQrcode();
    startIntervalGetCode();
  };

  /**
   * @description 查看钱包余额明细
   */
  const handleViewBalanceDetail = () => {
    if (!balanceInfo.detail || balanceInfo.detail.length === 0) {
      Toast.info(t('暂无余额明细'));
      return;
    }

    Dialog.alert({
      title: t('钱包余额明细'),
      message: (
        <div className={styles.balanceDetailDialog}>
          <div className={styles.totalBalance}>
            <div className={styles.label}>{t('钱包总余额')}</div>
            <div className={styles.value}>¥{balanceInfo.total?.toFixed(2) || '--'}</div>
          </div>
          {balanceInfo.detail?.map((item, index) => (
            <div key={index} className={styles.detailItem}>
              <div className={styles.accountName}>{item.accountName}</div>
              <div className={styles.accountBalance}>
                ¥{item.accountBalance?.toFixed(2) || '0.00'}
              </div>
            </div>
          ))}
        </div>
      ),
      confirmButtonText: t('我知道了'),
    });
  };

  const handleWechatPayConfirm = usePersistFn(() => {
    // 实际项目中这里会调用微信支付SDK
    Toast.info(t('正在调用微信支付...')); // 模拟调用
    closeWechatPayModal();
    // 假设支付已发起，开始轮询支付结果
    startPolling();
  });

  const handleWechatPayCancel = usePersistFn(() => {
    Toast.info(t('订单已取消'));
    closeWechatPayModal();
    // 用户取消订单后，继续轮询
    startPolling();
  });

  return (
    <div className={styles.container}>
      <div className={styles.code}>
        <QRCode
          size={220}
          className={styles.qrCode}
          status={loading ? 'loading' : 'active'}
          value={codeValue}
        />
        <div className={styles.subDes}>
          <p>
            <Button text type="primary" onClick={handleRefreshCode}>
              {t('点击刷新消费码')}
            </Button>
          </p>
        </div>
      </div>
      <div style={{ padding: '0 12px 12px', background: '#fff' }}>
        <div className={styles.walletInfo}>
          <div className={styles.walletCard}>
            <div className={styles.title}>
              <div style={{ fontSize: '20px', color: '#f56c0a', marginRight: '6px' }}>
                <Icon name="pc-charge-shineout-fill" />
              </div>
              {t('钱包余额')}
            </div>
            <div className={styles.balanceRow}>
              <div className={styles.balanceAmount}>¥{balanceInfo.total?.toFixed(2) || '0.00'}</div>
              <Button
                text
                type="primary"
                className={styles.detailButton}
                onClick={handleViewBalanceDetail}
              >
                <Icon name="help-outline" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* {showWechatPayModal && paymentInfo && (
        <div className={styles.customModalOverlay}>
          <div className={styles.customModal}>
            <div className={styles.customModalTitle}>
              {t('钱包可用余额不足,是否使用微信支付差额')}
              <span style={{ color: '#f56c0a' }}>
                {' '}
                ¥{paymentInfo.wechatPay?.toFixed(2) || '0.00'}
              </span>
            </div>
            <div className={styles.customModalContent}>
              <div className={styles.paymentDetails}>
                <div className={styles.paymentRow}>
                  <span>{t('合计需支付')}</span>
                  <span>¥{paymentInfo.totalPay?.toFixed(2) || '0.00'}</span>
                </div>
                <div className={styles.paymentRow}>
                  <span>{t('钱包余额')}</span>
                  <span>¥{paymentInfo.walletPay?.toFixed(2) || '0.00'}</span>
                </div>
                <div className={styles.paymentRow}>
                  <span>{t('微信支付差额')}</span>
                  <span>¥{paymentInfo.wechatPay?.toFixed(2) || '0.00'}</span>
                </div>
              </div>
            </div>
            <div className={styles.customModalFooter}>
              <Button
                type="primary"
                className={`${styles.customModalButton} ${styles.customModalButtonPrimary}`}
                onClick={handleWechatPayConfirm}
              >
                {t('使用微信支付差额')}
              </Button>
              <Button className={styles.customModalButton} onClick={handleWechatPayCancel}>
                {t('取消')}
              </Button>
            </div>
          </div>
        </div>
      )} */}
    </div>
  );
};

export default ConsumerQrcodePage;
