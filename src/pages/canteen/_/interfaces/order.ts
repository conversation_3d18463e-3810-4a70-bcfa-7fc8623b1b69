import { t } from '@shein-bbl/react';

export interface IOrderDetailItem {
  /** 菜品名称 */
  dishName?: string;
  /** 菜品图片 */
  dishPic?: string;
  /** 菜品价格 */
  dishPrice?: number;
  /** 数量 */
  dishNum?: number;
}

interface IPayDetailItem {
  /** 金额 */
  payAmount: number;
  /** 渠道中文 */
  payChannelType: string;
}

/** 订单状态 */
export enum EOrderStatus {
  /** 待支付 */
  PENDING = 0,
  /** 已预定 */
  RESERVED = 1,
  /** 已完成 */
  COMPLETED = 2,
  /** 已取消 */
  CANCELLED = 3,
  /** 已退款 */
  REFUNDED = 4,
}

export const orderStatusMap = {
  [EOrderStatus.PENDING]: t('待支付'),
  [EOrderStatus.RESERVED]: t('已预定'),
  [EOrderStatus.COMPLETED]: t('已完成'),
  [EOrderStatus.CANCELLED]: t('已取消'),
  [EOrderStatus.REFUNDED]: t('已退款'),
};

/** 订单类型 */
export enum EOrderType {
  /** 定餐 */
  ORDER = 0,
  /** 现场点餐 */
  ORDER_OFFLINE = 1,
}

export const orderTypeMap = {
  [EOrderType.ORDER]: t('定餐'),
  [EOrderType.ORDER_OFFLINE]: t('现场点餐'),
};

export interface IOrderDetail {
  /** 订单状态 */
  orderStatus?: EOrderStatus;
  /** 餐段类型名称	 */
  mealTimePeriodTypeName?: string;
  /** 如11:00~13:00 */
  timePeriod?: string;
  /** yyyy-MM-dd */
  date?: string;
  /** 食堂名称 */
  canteenName?: string;
  /** 档口名称 */
  stallName?: string;
  /** 0为未评价，1-5已评价 */
  evaluateScore?: number;
  /** 核销码 */
  qrcode?: string;
  /** 订单明细 */
  orderDetail?: IOrderDetailItem[];
  /** 是否可以取消预定 */
  canCancel?: boolean;
  /** 是否可以评价 */
  canEvaluate?: boolean;
  /** 订单类型 */
  orderType?: EOrderType;
  /** 订单预定时间(支付时间) */
  bookingTime?: string;
  /** 订单完成时间(核销时间) */
  finishTime?: string;
  /** 支付方式 */
  payDetail: IPayDetailItem[];
  /** 订单编号 */
  orderNo: string;
}
