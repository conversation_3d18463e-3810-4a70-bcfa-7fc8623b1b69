import React from 'react';
import Icon from '@/_/components/Icon';
import { EOrderStatus, IOrderDetail, orderStatusMap } from '@/pages/canteen/_/interfaces/order';
import { t } from '@shein-bbl/react';
import classNames from 'classnames';
import QRCodeCanvas from 'qrcode.react';
import styles from './index.less';
interface IOrderStatusCardProps {
  detail: IOrderDetail;
}

/**
 * @description 订单状态卡片
 * @param {IOrderDetail} detail 订单详情
 * @returns {React.ReactElement} 订单状态卡片
 */
const OrderStatusCard: React.FC<IOrderStatusCardProps> = ({ detail }): React.ReactElement => {
  let statusIcon: React.ReactNode;
  switch (detail.orderStatus) {
    case EOrderStatus.PENDING:
      statusIcon = <Icon name="pending" fontSize={22} color="#FF6A00" />;
      break;
    case EOrderStatus.RESERVED:
      statusIcon = <Icon name="m-service-multic-shineout" fontSize={22} color="#996F37" />;
      break;
    case EOrderStatus.COMPLETED:
      statusIcon = <Icon name="m-check-circle-shineout" fontSize={22} color="#00A85F" />;
      break;
    case EOrderStatus.CANCELLED:
    case EOrderStatus.REFUNDED:
      statusIcon = <Icon name="fail" fontSize={22} color="#999DA8" />;
      break;
    default:
      break;
  }

  return (
    <div className={styles.card}>
      <div
        className={classNames(styles.header, {
          [styles.reserved]: detail.orderStatus === EOrderStatus.RESERVED,
        })}
      >
        <div className={styles.statusIcon}>{statusIcon}</div>
        <div>
          <div className={styles.statusText}>{orderStatusMap[detail.orderStatus]}</div>
          {[EOrderStatus.CANCELLED, EOrderStatus.REFUNDED].includes(detail.orderStatus) ? (
            <div className={styles.refundText}>{t('退款金额已按原支付方式退回')}</div>
          ) : (
            <div>
              <span className={styles.timeLabel}>{t('就餐时段：')}</span>
              <span className={styles.timeValue}>{detail.timePeriod}</span>
            </div>
          )}
        </div>
      </div>
      {detail.orderStatus === EOrderStatus.RESERVED && (
        <>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="block"
            width="100%"
            height="20"
            viewBox="0 0 351 20"
            fill="none"
            preserveAspectRatio="none"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M10 10C10 15.5228 5.52285 20 0 20H351C345.477 20 341 15.5228 341 10C341 4.47715 345.477 0 351 0H0C5.52285 0 10 4.47715 10 10ZM10 10V9H12.0183V10H10ZM341 10V9H338.982V10H341ZM16.0549 10H20.0915V9H16.0549V10ZM24.128 10H28.1646V9H24.128V10ZM32.2012 10H36.2378V9H32.2012V10ZM40.2744 10H44.311V9H40.2744V10ZM48.3476 10H52.3841V9H48.3476V10ZM56.4207 10H60.4573V9H56.4207V10ZM64.4939 10H68.5305V9H64.4939V10ZM72.5671 10H76.6037V9H72.5671V10ZM80.6403 10H84.6768V9H80.6403V10ZM88.7134 10H92.75V9H88.7134V10ZM96.7866 10H100.823V9H96.7866V10ZM104.86 10H108.896V9H104.86V10ZM112.933 10H116.969V9H112.933V10ZM121.006 10H125.043V9H121.006V10ZM129.079 10H133.116V9H129.079V10ZM137.152 10H141.189V9H137.152V10ZM145.226 10H149.262V9H145.226V10ZM153.299 10H157.335V9H153.299V10ZM161.372 10H165.409V9H161.372V10ZM169.445 10H173.482V9H169.445V10ZM177.518 10H181.555V9H177.518V10ZM185.591 10H189.628V9H185.591V10ZM193.665 10H197.701V9H193.665V10ZM201.738 10H205.774V9H201.738V10ZM209.811 10H213.848V9H209.811V10ZM217.884 10H221.921V9H217.884V10ZM225.957 10H229.994V9H225.957V10ZM234.031 10H238.067V9H234.031V10ZM242.104 10H246.14V9H242.104V10ZM250.177 10H254.214V9H250.177V10ZM258.25 10H262.287V9H258.25V10ZM266.323 10H270.36V9H266.323V10ZM274.396 10H278.433V9H274.396V10ZM282.47 10H286.506V9H282.47V10ZM290.543 10H294.579V9H290.543V10ZM298.616 10H302.653V9H298.616V10ZM306.689 10H310.726V9H306.689V10ZM314.762 10H318.799V9H314.762V10ZM322.836 10H326.872V9H322.836V10ZM330.909 10H334.945V9H330.909V10Z"
              fill="white"
            />
          </svg>
          {detail.qrcode ? (
            <div className={styles.qrSection}>
              <div className={styles.qrTip}>{t('请前往预定档口扫码取餐')}</div>
              <div className={styles.qrWrapper}>
                <QRCodeCanvas value={detail.qrcode} size={220} fgColor="#F56C0A" />
              </div>
            </div>
          ) : (
            <div className={styles.qrNotice}>{t('定餐码将在指定日期的就餐时间段内展示')}</div>
          )}
        </>
      )}
    </div>
  );
};

export default OrderStatusCard;
