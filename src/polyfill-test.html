<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Polyfill 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Polyfill 功能测试</h1>
    <div id="test-results"></div>

    <!-- 引入我们的 polyfill 脚本 -->
    <script>
        // 基础 polyfills 用于老旧 WebView 环境
        (function() {
            // Promise polyfill for very old environments
            if (typeof Promise === 'undefined') {
                window.Promise = function(executor) {
                    var self = this;
                    self.state = 'pending';
                    self.value = undefined;
                    self.handlers = [];

                    function resolve(result) {
                        if (self.state === 'pending') {
                            self.state = 'fulfilled';
                            self.value = result;
                            self.handlers.forEach(handle);
                            self.handlers = null;
                        }
                    }

                    function reject(error) {
                        if (self.state === 'pending') {
                            self.state = 'rejected';
                            self.value = error;
                            self.handlers.forEach(handle);
                            self.handlers = null;
                        }
                    }

                    function handle(handler) {
                        if (self.state === 'pending') {
                            self.handlers.push(handler);
                        } else {
                            if (self.state === 'fulfilled' && typeof handler.onFulfilled === 'function') {
                                handler.onFulfilled(self.value);
                            }
                            if (self.state === 'rejected' && typeof handler.onRejected === 'function') {
                                handler.onRejected(self.value);
                            }
                        }
                    }

                    this.then = function(onFulfilled, onRejected) {
                        return new Promise(function(resolve, reject) {
                            handle({
                                onFulfilled: function(result) {
                                    try {
                                        resolve(onFulfilled ? onFulfilled(result) : result);
                                    } catch (ex) {
                                        reject(ex);
                                    }
                                },
                                onRejected: function(error) {
                                    try {
                                        resolve(onRejected ? onRejected(error) : error);
                                    } catch (ex) {
                                        reject(ex);
                                    }
                                }
                            });
                        });
                    };

                    executor(resolve, reject);
                };
            }

            // Object.assign polyfill
            if (typeof Object.assign !== 'function') {
                Object.assign = function(target) {
                    if (target == null) {
                        throw new TypeError('Cannot convert undefined or null to object');
                    }
                    var to = Object(target);
                    for (var index = 1; index < arguments.length; index++) {
                        var nextSource = arguments[index];
                        if (nextSource != null) {
                            for (var nextKey in nextSource) {
                                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                                    to[nextKey] = nextSource[nextKey];
                                }
                            }
                        }
                    }
                    return to;
                };
            }

            // Array.prototype.includes polyfill
            if (!Array.prototype.includes) {
                Array.prototype.includes = function(searchElement, fromIndex) {
                    if (this == null) {
                        throw new TypeError('"this" is null or not defined');
                    }
                    var o = Object(this);
                    var len = parseInt(o.length) || 0;
                    if (len === 0) {
                        return false;
                    }
                    var n = parseInt(fromIndex) || 0;
                    var k;
                    if (n >= 0) {
                        k = n;
                    } else {
                        k = len + n;
                        if (k < 0) {
                            k = 0;
                        }
                    }
                    function sameValueZero(x, y) {
                        return x === y || (typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y));
                    }
                    for (; k < len; k++) {
                        if (sameValueZero(o[k], searchElement)) {
                            return true;
                        }
                    }
                    return false;
                };
            }

            // String.prototype.includes polyfill
            if (!String.prototype.includes) {
                String.prototype.includes = function(search, start) {
                    if (typeof start !== 'number') {
                        start = 0;
                    }
                    if (start + search.length > this.length) {
                        return false;
                    } else {
                        return this.indexOf(search, start) !== -1;
                    }
                };
            }

            // String.prototype.startsWith polyfill
            if (!String.prototype.startsWith) {
                String.prototype.startsWith = function(searchString, position) {
                    position = position || 0;
                    return this.substr(position, searchString.length) === searchString;
                };
            }

            // String.prototype.endsWith polyfill
            if (!String.prototype.endsWith) {
                String.prototype.endsWith = function(searchString, length) {
                    if (length === undefined || length > this.length) {
                        length = this.length;
                    }
                    return this.substring(length - searchString.length, length) === searchString;
                };
            }

            // Array.prototype.find polyfill
            if (!Array.prototype.find) {
                Array.prototype.find = function(predicate) {
                    if (this == null) {
                        throw new TypeError('"this" is null or not defined');
                    }
                    var o = Object(this);
                    var len = parseInt(o.length) || 0;
                    if (typeof predicate !== 'function') {
                        throw new TypeError('predicate must be a function');
                    }
                    var thisArg = arguments[1];
                    var k = 0;
                    while (k < len) {
                        var kValue = o[k];
                        if (predicate.call(thisArg, kValue, k, o)) {
                            return kValue;
                        }
                        k++;
                    }
                    return undefined;
                };
            }

            // Array.prototype.findIndex polyfill
            if (!Array.prototype.findIndex) {
                Array.prototype.findIndex = function(predicate) {
                    if (this == null) {
                        throw new TypeError('"this" is null or not defined');
                    }
                    var o = Object(this);
                    var len = parseInt(o.length) || 0;
                    if (typeof predicate !== 'function') {
                        throw new TypeError('predicate must be a function');
                    }
                    var thisArg = arguments[1];
                    var k = 0;
                    while (k < len) {
                        var kValue = o[k];
                        if (predicate.call(thisArg, kValue, k, o)) {
                            return k;
                        }
                        k++;
                    }
                    return -1;
                };
            }

            // console polyfill for environments without console
            if (typeof console === 'undefined') {
                window.console = {
                    log: function() {},
                    error: function() {},
                    warn: function() {},
                    info: function() {},
                    debug: function() {}
                };
            }
        })();
    </script>

    <script>
        // 测试函数
        function runTests() {
            var results = document.getElementById('test-results');
            var tests = [
                {
                    name: 'Promise 支持',
                    test: function() {
                        return typeof Promise !== 'undefined' && typeof Promise.prototype.then === 'function';
                    }
                },
                {
                    name: 'Object.assign 支持',
                    test: function() {
                        var obj1 = { a: 1 };
                        var obj2 = { b: 2 };
                        var result = Object.assign(obj1, obj2);
                        return result.a === 1 && result.b === 2;
                    }
                },
                {
                    name: 'Array.prototype.includes 支持',
                    test: function() {
                        var arr = [1, 2, 3];
                        return arr.includes(2) === true && arr.includes(4) === false;
                    }
                },
                {
                    name: 'String.prototype.includes 支持',
                    test: function() {
                        var str = 'hello world';
                        return str.includes('world') === true && str.includes('foo') === false;
                    }
                },
                {
                    name: 'String.prototype.startsWith 支持',
                    test: function() {
                        var str = 'hello world';
                        return str.startsWith('hello') === true && str.startsWith('world') === false;
                    }
                },
                {
                    name: 'String.prototype.endsWith 支持',
                    test: function() {
                        var str = 'hello world';
                        return str.endsWith('world') === true && str.endsWith('hello') === false;
                    }
                },
                {
                    name: 'Array.prototype.find 支持',
                    test: function() {
                        var arr = [1, 2, 3, 4, 5];
                        var result = arr.find(function(x) { return x > 3; });
                        return result === 4;
                    }
                },
                {
                    name: 'Array.prototype.findIndex 支持',
                    test: function() {
                        var arr = [1, 2, 3, 4, 5];
                        var result = arr.findIndex(function(x) { return x > 3; });
                        return result === 3;
                    }
                },
                {
                    name: 'console 对象支持',
                    test: function() {
                        return typeof console !== 'undefined' && typeof console.log === 'function';
                    }
                },
                {
                    name: '空值合并运算符 (??) 支持检测',
                    test: function() {
                        // 检测是否支持空值合并运算符
                        try {
                            // 使用 Function 构造函数来避免语法错误
                            var testFunc = new Function('return null ?? "fallback"');
                            var result = testFunc();
                            return result === "fallback";
                        } catch (e) {
                            // 如果不支持，应该通过 Babel 转换
                            return false;
                        }
                    }
                },
                {
                    name: '可选链操作符 (?.) 支持检测',
                    test: function() {
                        try {
                            var testFunc = new Function('var obj = {}; return obj?.nonExistent');
                            var result = testFunc();
                            return result === undefined;
                        } catch (e) {
                            return false;
                        }
                    }
                }
            ];

            tests.forEach(function(testCase) {
                var div = document.createElement('div');
                div.className = 'test-item';

                try {
                    var passed = testCase.test();
                    div.className += passed ? ' success' : ' error';
                    div.innerHTML = '<strong>' + testCase.name + '</strong>: ' + (passed ? '✅ 通过' : '❌ 失败');
                } catch (error) {
                    div.className += ' error';
                    div.innerHTML = '<strong>' + testCase.name + '</strong>: ❌ 错误 - ' + error.message;
                }

                results.appendChild(div);
            });
        }

        // 页面加载完成后运行测试
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', runTests);
        } else {
            runTests();
        }
    </script>
</body>
</html>
